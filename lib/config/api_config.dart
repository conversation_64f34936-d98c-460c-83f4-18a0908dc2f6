/// Configuration for API endpoints and settings
class ApiConfig {
  // InternVL Gradio API Configuration (Primary)
  static const String internVLGradioUrl = 'https://opengvlab-internvl.hf.space';
  static const String internVLSpaceId = 'OpenGVLab/InternVL';

  // Hugging Face Inference Providers API Configuration (Fallback)
  static const String huggingFaceRouterUrl = 'https://router.huggingface.co/hf-inference/models';

  // Vision-Language Models available through the new API
  static const List<String> visionLanguageModels = [
    'meta-llama/Llama-3.2-11B-Vision-Instruct',
    'meta-llama/Llama-3.2-90B-Vision-Instruct',
    'microsoft/Phi-3.5-vision-instruct',
  ];

  // Fallback: Legacy Inference API (may still work for some models)
  static const String legacyInferenceUrl = 'https://api-inference.huggingface.co/models';
  static const List<String> legacyVisionModels = [
    'Salesforce/blip-image-captioning-large',
    'Salesforce/blip-image-captioning-base',
    'microsoft/git-large-coco',
    'nlpconnect/vit-gpt2-image-captioning',
  ];
  
  // Hugging Face API Token
  static const String huggingFaceToken = '*************************************';

  // OpenRouter API Configuration (Alternative)
  static const String openRouterUrl = 'https://openrouter.ai/api/v1/chat/completions';
  static const String openRouterToken = 'YOUR_OPENROUTER_API_KEY'; // Replace with actual key

  // OpenRouter Vision Models (Free/Cheap options)
  static const List<String> openRouterVisionModels = [
    'meta-llama/llama-3.2-11b-vision-instruct:free',
    'meta-llama/llama-3.2-90b-vision-instruct',
    'google/gemini-flash-1.5',
    'anthropic/claude-3-haiku',
  ];

  // API timeout settings (reduced with authenticated access)
  static const Duration apiTimeout = Duration(seconds: 45);
  static const Duration connectionTimeout = Duration(seconds: 20);
  
  // Default prompts for different analysis types
  static const Map<String, String> defaultPrompts = {
    'general': 'Analyze this image in detail. Describe what you see, including objects, colors, composition, and any notable features.',
    'objects': 'Identify and list all the objects visible in this image.',
    'colors': 'Describe the color palette and color scheme of this image.',
    'composition': 'Analyze the composition, lighting, and artistic elements of this image.',
    'text': 'Extract and transcribe any text visible in this image.',
    'items': '''Analyze this image and identify ALL visible products, items, or packages. Look for:

PRODUCTS TO FIND:
- Beverages: Beer cans/bottles, soft drinks, water, energy drinks, juice
- Food items: Snacks, packaged foods, canned goods
- Any consumer products with visible labels or packaging
- Items in multipacks, cases, or bulk packaging

SEARCH STRATEGY:
1. Scan the entire image systematically
2. Look for brand names, product labels, and text on packages
3. Identify packaging types (cans, bottles, boxes, bags)
4. Note any visible prices or size information
5. Include partially visible items if identifiable

RESPONSE FORMAT - Return ONLY valid JSON:
{
  "items": [
    {
      "name": "Product name as shown",
      "brand": "Brand name if visible",
      "size": "Size/quantity (330ml, 12-pack, etc.)",
      "productType": "Can/Bottle/Box/Bag/etc.",
      "price": "Price with currency if visible",
      "description": "What you see",
      "confidence": 0.8
    }
  ]
}

EXAMPLES:
- Corona Extra Beer 330ml Can \$4.99
- Coca-Cola Classic 500ml Bottle \$2.49
- Lay's Potato Chips 150g Bag \$3.29

If NO products are visible, return: {"items": []}

CRITICAL: Respond with ONLY the JSON object, no other text.''',
  };
  
  // Model parameters
  static const Map<String, dynamic> defaultModelParams = {
    'temperature': 0.7,
    'max_tokens': 1000,
    'top_p': 0.9,
  };
  
  // Feature flags
  static const bool enableFallbackToMockData = true;
  static const bool enableRetryOnFailure = true;
  static const int maxRetryAttempts = 3;
}
