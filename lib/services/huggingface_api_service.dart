import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/item_detection_result.dart';

/// Service for interacting with Hugging Face InternVL API
/// 
/// This service handles image analysis using the InternVL model hosted on
/// Hugging Face Spaces. It converts images to base64 and sends them to the
/// Gradio API endpoint for analysis.
class HuggingFaceApiService {
  /// Current retry attempt counter
  int _retryCount = 0;
  
  /// Analyzes an image using the InternVL model
  ///
  /// [imageFile] can be either a File (mobile/desktop) or Uint8List (web)
  /// [prompt] is the question or instruction for the model (optional)
  /// [analysisType] determines the type of analysis to perform
  ///
  /// Returns a [VisionAnalysisResult] containing the analysis results
  Future<VisionAnalysisResult> analyzeImage({
    required dynamic imageFile,
    String? prompt,
    String analysisType = 'general',
  }) async {
    _retryCount = 0;
    return _analyzeImageWithRetry(imageFile, prompt, analysisType);
  }

  /// Analyzes an image specifically for item detection
  ///
  /// [imageFile] can be either a File (mobile/desktop) or Uint8List (web)
  /// [prompt] is the question or instruction for the model (optional)
  ///
  /// Returns an [ItemDetectionResult] containing detected items with structured data
  Future<ItemDetectionResult> analyzeImageForItems({
    required dynamic imageFile,
    String? prompt,
  }) async {
    try {
      // First try InternVL Gradio API
      try {
        print('🔬 Trying InternVL Gradio API...');
        final result = await _analyzeWithInternVLGradio(imageFile, prompt);
        return result;
      } catch (e) {
        print('⚠️ InternVL Gradio API failed: $e');
      }

      // Fallback to existing API methods
      print('🔄 Falling back to existing API methods...');
      final result = await analyzeImage(
        imageFile: imageFile,
        prompt: prompt,
        analysisType: 'items',
      );

      // Try to parse the response as JSON for item detection
      return _parseItemDetectionResponse(result.description, result.rawResponse);
    } catch (e) {
      print('🚨 Error in analyzeImageForItems: $e');

      // Try one more fallback: basic image description analysis
      print('🔄 Trying basic image description analysis...');
      try {
        final basicResult = await analyzeImage(
          imageFile: imageFile,
          analysisType: 'general',
        );

        // Try to extract items from the basic description
        final extractedResult = _parseTextForItems(basicResult.description);
        if (extractedResult.items.isNotEmpty) {
          print('✅ Extracted ${extractedResult.items.length} items from basic analysis');
          return extractedResult;
        }
      } catch (basicError) {
        print('⚠️ Basic analysis also failed: $basicError');
      }

      // Return mock data if enabled
      if (ApiConfig.enableFallbackToMockData) {
        print('🎭 Falling back to mock item detection data');
        return ItemDetectionResult.mock();
      }

      // Return empty result with helpful error message
      return ItemDetectionResult(
        items: [],
        rawResponse: 'All analysis strategies failed. Check console logs for details. Try the "Test Mock" button to see the interface working.',
        errorMessage: 'No items could be detected using any available method. This could be due to API issues or image quality. Try the "Test Mock" button to see sample results.',
      );
    }
  }

  /// Analyzes image using Hugging Face Router API with a specific model
  Future<ItemDetectionResult> _analyzeWithHuggingFaceRouter(dynamic imageFile, String? prompt, String model) async {
    try {
      // Convert image to base64
      final base64Image = await _convertImageToBase64(imageFile);

      // Use the items prompt if no custom prompt provided
      final effectivePrompt = prompt ?? ApiConfig.defaultPrompts['items']!;

      // Prepare chat completion payload
      final payload = {
        "messages": [
          {
            "role": "user",
            "content": [
              {
                "type": "text",
                "text": effectivePrompt,
              },
              {
                "type": "image_url",
                "image_url": {
                  "url": base64Image,
                },
              },
            ],
          },
        ],
        "model": model,
        "stream": false,
        "max_tokens": 1000,
        "temperature": 0.7,
      };

      final fullUrl = '${ApiConfig.huggingFaceRouterUrl}/$model/v1/chat/completions';

      final response = await http.post(
        Uri.parse(fullUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${ApiConfig.huggingFaceToken}',
        },
        body: jsonEncode(payload),
      ).timeout(ApiConfig.apiTimeout);

      if (response.statusCode != 200) {
        throw ApiException('Router API failed: ${response.statusCode} - ${response.body}');
      }

      final responseData = jsonDecode(response.body);
      print('📊 Router API response: ${responseData.toString().substring(0, 200)}...');

      // Extract the response text
      String analysisText = '';
      if (responseData.containsKey('choices') && responseData['choices'] is List) {
        final choices = responseData['choices'] as List;
        if (choices.isNotEmpty) {
          final firstChoice = choices[0] as Map<String, dynamic>;
          if (firstChoice.containsKey('message')) {
            final message = firstChoice['message'] as Map<String, dynamic>;
            analysisText = message['content'] as String? ?? '';
          }
        }
      }

      if (analysisText.isEmpty) {
        throw ApiException('No analysis text found in Router API response');
      }

      // Parse the response for item detection
      return _parseItemDetectionResponse(analysisText, responseData);

    } catch (e) {
      throw ApiException('Router API error: $e');
    }
  }

  /// Analyzes image using Legacy Inference API with a specific model
  Future<ItemDetectionResult> _analyzeWithLegacyInference(dynamic imageFile, String? prompt, String model) async {
    try {
      // For legacy API, we send the image as binary data
      final imageBytes = await _getImageBytes(imageFile);

      final fullUrl = '${ApiConfig.legacyInferenceUrl}/$model';

      final response = await http.post(
        Uri.parse(fullUrl),
        headers: {
          'Content-Type': 'application/octet-stream',
          'Authorization': 'Bearer ${ApiConfig.huggingFaceToken}',
        },
        body: imageBytes,
      ).timeout(ApiConfig.apiTimeout);

      if (response.statusCode != 200) {
        throw ApiException('Legacy API failed: ${response.statusCode} - ${response.body}');
      }

      final responseData = jsonDecode(response.body);
      print('📊 Legacy API response: ${responseData.toString()}');

      // Extract the response text
      String analysisText = '';
      if (responseData is List && responseData.isNotEmpty) {
        final firstResult = responseData[0];
        if (firstResult is Map<String, dynamic> && firstResult.containsKey('generated_text')) {
          analysisText = firstResult['generated_text'] as String;
        } else {
          analysisText = firstResult.toString();
        }
      } else if (responseData is Map<String, dynamic>) {
        analysisText = responseData['generated_text'] as String? ??
                     responseData['caption'] as String? ??
                     responseData.toString();
      }

      if (analysisText.isEmpty) {
        throw ApiException('No analysis text found in Legacy API response');
      }

      // For legacy models, we need to extract items from the description
      return _parseTextForItems(analysisText);

    } catch (e) {
      throw ApiException('Legacy API error: $e');
    }
  }

  /// Analyzes image using InternVL Gradio API with correct queue-based approach
  Future<ItemDetectionResult> _analyzeWithInternVLGradio(dynamic imageFile, String? prompt) async {
    try {
      print('🔬 Starting InternVL Gradio API analysis...');

      // Convert image to base64 for Gradio API
      final base64Image = await _convertImageToBase64(imageFile);

      // Use the items prompt if no custom prompt provided
      final effectivePrompt = prompt ?? ApiConfig.defaultPrompts['items']!;
      print('📝 Using prompt: ${effectivePrompt.substring(0, 100)}...');

      // Create a session hash for this conversation
      final sessionHash = _generateSessionHash();

      // Step 1: Use the /run/predict endpoint (as seen in network requests)
      final predictUrl = '${ApiConfig.internVLGradioUrl}/run/predict';

      // Prepare the payload based on the actual API structure
      final payload = {
        "data": [
          {
            "text": effectivePrompt,
            "files": [base64Image]
          },
          "请尽可能详细地回答用户的问题。" // System prompt
        ],
        "event_data": null,
        "fn_index": 0, // This corresponds to the add_text function
        "trigger_id": 10,
        "session_hash": sessionHash
      };

      print('🌐 Sending predict request to InternVL (Free API)...');
      final predictResponse = await http.post(
        Uri.parse(predictUrl),
        headers: {
          'Content-Type': 'application/json',
          // No authorization needed for public Gradio spaces
        },
        body: jsonEncode(payload),
      ).timeout(ApiConfig.apiTimeout);

      if (predictResponse.statusCode != 200) {
        print('❌ Predict failed: ${predictResponse.statusCode} - ${predictResponse.body}');
        throw ApiException('Gradio predict failed: ${predictResponse.statusCode} - ${predictResponse.body}');
      }

      print('✅ Predict successful: ${predictResponse.body.substring(0, 200)}...');

      // Step 2: Join the queue for processing
      final queueUrl = '${ApiConfig.internVLGradioUrl}/queue/join';

      final queuePayload = {
        "data": [
          0.2,    // temperature
          0.7,    // top_p
          1.1,    // repetition_penalty
          1024,   // max_new_tokens
          12      // max_input_tiles
        ],
        "event_data": null,
        "fn_index": 1, // This corresponds to the http_bot function
        "trigger_id": 11,
        "session_hash": sessionHash
      };

      print('🚀 Joining queue...');
      final queueResponse = await http.post(
        Uri.parse(queueUrl),
        headers: {
          'Content-Type': 'application/json',
          // No authorization needed for public Gradio spaces
        },
        body: jsonEncode(queuePayload),
      ).timeout(ApiConfig.apiTimeout);

      if (queueResponse.statusCode != 200) {
        print('❌ Queue join failed: ${queueResponse.statusCode} - ${queueResponse.body}');
        throw ApiException('Gradio queue join failed: ${queueResponse.statusCode} - ${queueResponse.body}');
      }

      print('✅ Queue joined: ${queueResponse.body.substring(0, 200)}...');

      // Step 3: Poll for results
      final dataUrl = '${ApiConfig.internVLGradioUrl}/queue/data';
      String analysisText = '';
      int pollAttempts = 0;
      const maxPollAttempts = 30; // 30 attempts with 2-second intervals = 1 minute max

      while (pollAttempts < maxPollAttempts && analysisText.isEmpty) {
        await Future.delayed(const Duration(seconds: 2));
        pollAttempts++;

        print('🔄 Polling for results (attempt $pollAttempts/$maxPollAttempts)...');

        try {
          final dataResponse = await http.get(
            Uri.parse('$dataUrl?session_hash=$sessionHash'),
            // No authorization needed for public Gradio spaces
          ).timeout(const Duration(seconds: 10));

          if (dataResponse.statusCode == 200) {
            final responseData = jsonDecode(dataResponse.body);
            print('📊 Poll response: ${dataResponse.body.substring(0, 300)}...');

            // Try to extract the analysis text from the response
            if (responseData is Map && responseData['msg'] == 'process_completed') {
              final output = responseData['output'];
              if (output is Map && output['data'] is List) {
                final data = output['data'];
                if (data.isNotEmpty) {
                  final firstItem = data[0];
                  if (firstItem is List && firstItem.isNotEmpty) {
                    // Try to get the bot response from conversation format
                    final lastMessage = firstItem.last;
                    if (lastMessage is List && lastMessage.length > 1) {
                      final botMessage = lastMessage[1];
                      if (botMessage is String) {
                        analysisText = botMessage;
                        break;
                      }
                    }
                  }
                }
              }
            }
          }
        } catch (e) {
          print('⚠️ Poll attempt $pollAttempts failed: $e');
        }
      }

      if (analysisText.isEmpty) {
        print('❌ No analysis text found after $pollAttempts polling attempts');
        throw ApiException('No analysis text found after polling - request may have timed out');
      }

      print('📝 Extracted analysis text: ${analysisText.substring(0, 200)}...');

      // Parse the response for item detection
      return _parseItemDetectionResponse(analysisText, {'gradio_response': analysisText});

    } catch (e) {
      print('❌ InternVL Gradio API error: $e');
      throw ApiException('InternVL Gradio API error: $e');
    }
  }

  /// Internal method that handles retries
  Future<VisionAnalysisResult> _analyzeImageWithRetry(
    dynamic imageFile,
    String? prompt,
    String analysisType,
  ) async {
    print('🔍 Starting image analysis (attempt ${_retryCount + 1})');

    try {
      // Use provided prompt or default based on analysis type
      final effectivePrompt = prompt ?? ApiConfig.defaultPrompts[analysisType] ?? ApiConfig.defaultPrompts['general']!;
      print('📝 Using prompt: $effectivePrompt');

      // Convert image to base64
      String base64Image = await _convertImageToBase64(imageFile);
      print('🖼️ Image converted to base64 (${base64Image.length} characters)');

      // Prepare the request payload
      final payload = {
        "data": [
          base64Image,
          effectivePrompt,
          ApiConfig.defaultModelParams,
        ],
        "fn_index": 0, // Usually 0 for the main predict function
      };

      // First try new Hugging Face Inference Providers API (Chat Completion)
      print('🤗 Trying Hugging Face Inference Providers API with ${ApiConfig.visionLanguageModels.length} models...');
      print('🔑 Using authenticated access with HF token');

      for (int i = 0; i < ApiConfig.visionLanguageModels.length; i++) {
        final model = ApiConfig.visionLanguageModels[i];
        final fullUrl = '${ApiConfig.huggingFaceRouterUrl}/$model/v1/chat/completions';

        try {
          print('🌐 Attempting Chat Completion API call to: $model');

          // Convert image to base64 for the new API
          final base64Image = await _convertImageToBase64(imageFile);

          // Prepare chat completion payload
          final payload = {
            "messages": [
              {
                "role": "user",
                "content": [
                  {
                    "type": "text",
                    "text": effectivePrompt,
                  },
                  {
                    "type": "image_url",
                    "image_url": {
                      "url": base64Image,
                    },
                  },
                ],
              },
            ],
            "model": model,
            "stream": false,
            "max_tokens": 1000,
            "temperature": 0.7,
          };

          final response = await http.post(
            Uri.parse(fullUrl),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${ApiConfig.huggingFaceToken}',
            },
            body: jsonEncode(payload),
          ).timeout(ApiConfig.apiTimeout);

          print('📡 Response received: ${response.statusCode}');

          if (response.statusCode == 200) {
            print('✅ Chat Completion API call successful! Parsing response...');
            final responseData = jsonDecode(response.body);
            print('📊 Response data: ${responseData.toString().substring(0, 200)}...');
            return _parseChatCompletionResponse(responseData, effectivePrompt);
          } else if (response.statusCode == 503) {
            print('⚠️ Model loading (503), trying next model...');
            continue;
          } else if (response.statusCode == 429) {
            print('⚠️ Rate limited (429), trying next model...');
            continue;
          } else {
            print('❌ Chat Completion API request failed with status ${response.statusCode}');
            print('📄 Response body: ${response.body}');
            continue;
          }
        } on SocketException catch (e) {
          print('🔌 Network error for model $i: $e');
          continue;
        } on http.ClientException catch (e) {
          print('🌐 HTTP client error for model $i: $e');
          continue;
        } on Exception catch (e) {
          print('⚠️ Unexpected error for model $i: $e');
          continue;
        }
      }

      // If new API fails, try legacy Inference API as fallback
      print('📦 New API failed, trying legacy Inference API...');

      for (int i = 0; i < ApiConfig.legacyVisionModels.length; i++) {
        final model = ApiConfig.legacyVisionModels[i];
        final fullUrl = '${ApiConfig.legacyInferenceUrl}/$model';

        try {
          print('🌐 Attempting Legacy API call to: $model');

          // For legacy API, we send the image as binary data
          final imageBytes = await _getImageBytes(imageFile);

          final response = await http.post(
            Uri.parse(fullUrl),
            headers: {
              'Content-Type': 'application/octet-stream',
              'Authorization': 'Bearer ${ApiConfig.huggingFaceToken}',
            },
            body: imageBytes,
          ).timeout(ApiConfig.apiTimeout);

          print('📡 Response received: ${response.statusCode}');

          if (response.statusCode == 200) {
            print('✅ Legacy API call successful! Parsing response...');
            final responseData = jsonDecode(response.body);
            print('📊 Response data: ${responseData.toString()}');
            return _parseInferenceResponse(responseData, effectivePrompt);
          } else if (response.statusCode == 503) {
            print('⚠️ Model loading (503), trying next model...');
            continue;
          } else if (response.statusCode == 429) {
            print('⚠️ Rate limited (429), trying next model...');
            continue;
          } else {
            print('❌ Legacy API request failed with status ${response.statusCode}');
            print('📄 Response body: ${response.body}');
            continue;
          }
        } on SocketException catch (e) {
          print('🔌 Network error for model $i: $e');
          continue;
        } on http.ClientException catch (e) {
          print('🌐 HTTP client error for model $i: $e');
          continue;
        } on Exception catch (e) {
          print('⚠️ Unexpected error for model $i: $e');
          continue;
        }
      }

      // If all endpoints failed, throw an exception
      print('💥 All API endpoints failed!');
      throw ApiException('All API endpoints are currently unavailable');

    } catch (e) {
      print('🚨 Error in _analyzeImageWithRetry: $e');

      if (e is ApiException) {
        // Check if we should retry
        if (ApiConfig.enableRetryOnFailure && _retryCount < ApiConfig.maxRetryAttempts) {
          _retryCount++;
          print('🔄 Retrying in ${_retryCount * 2} seconds... (attempt $_retryCount/${ApiConfig.maxRetryAttempts})');
          await Future.delayed(Duration(seconds: _retryCount * 2)); // Exponential backoff
          return _analyzeImageWithRetry(imageFile, prompt, analysisType);
        }

        // If retries exhausted or fallback enabled, return mock data
        if (ApiConfig.enableFallbackToMockData) {
          print('🎭 Falling back to mock data');
          return VisionAnalysisResult.mock();
        }

        rethrow;
      }
      throw ApiException('Failed to analyze image: $e');
    }
  }
  
  /// Gets image bytes for Inference API
  Future<Uint8List> _getImageBytes(dynamic imageFile) async {
    try {
      if (kIsWeb) {
        // Web platform - imageFile is Uint8List
        return imageFile as Uint8List;
      } else {
        // Mobile/Desktop platform - imageFile is File
        final file = imageFile as File;
        return await file.readAsBytes();
      }
    } catch (e) {
      throw ApiException('Failed to get image bytes: $e');
    }
  }

  /// Converts image file to base64 string (for Spaces API)
  Future<String> _convertImageToBase64(dynamic imageFile) async {
    try {
      final bytes = await _getImageBytes(imageFile);

      // Convert to base64 with data URL prefix
      final base64String = base64Encode(bytes);
      return 'data:image/jpeg;base64,$base64String';
    } catch (e) {
      throw ApiException('Failed to convert image to base64: $e');
    }
  }
  
  /// Parses the Chat Completion API response (new format)
  VisionAnalysisResult _parseChatCompletionResponse(Map<String, dynamic> responseData, String prompt) {
    try {
      String analysisText = '';

      // Chat completion response format
      if (responseData.containsKey('choices') && responseData['choices'] is List) {
        final choices = responseData['choices'] as List;
        if (choices.isNotEmpty) {
          final firstChoice = choices[0] as Map<String, dynamic>;
          if (firstChoice.containsKey('message')) {
            final message = firstChoice['message'] as Map<String, dynamic>;
            analysisText = message['content'] as String? ?? 'No content available';
          }
        }
      }

      if (analysisText.isEmpty) {
        analysisText = 'Unable to parse response';
      }

      // Add context about the prompt used
      final fullDescription = 'AI Vision Analysis: $analysisText';

      return VisionAnalysisResult(
        description: fullDescription,
        confidence: _extractConfidence(analysisText),
        detectedObjects: _extractObjects(analysisText),
        colors: _extractColors(analysisText),
        tags: _extractTags(analysisText),
        rawResponse: {'chat_completion': responseData, 'prompt': prompt},
        isMockData: false,
      );
    } catch (e) {
      throw ApiException('Failed to parse Chat Completion response: $e');
    }
  }

  /// Parses the Legacy Inference API response
  VisionAnalysisResult _parseInferenceResponse(dynamic responseData, String prompt) {
    try {
      String analysisText = '';

      if (responseData is List && responseData.isNotEmpty) {
        // BLIP and similar models return a list with generated_text
        final firstResult = responseData[0];
        if (firstResult is Map<String, dynamic> && firstResult.containsKey('generated_text')) {
          analysisText = firstResult['generated_text'] as String;
        } else {
          analysisText = firstResult.toString();
        }
      } else if (responseData is Map<String, dynamic>) {
        // Some models return a map directly
        analysisText = responseData['generated_text'] as String? ??
                     responseData['caption'] as String? ??
                     responseData.toString();
      } else {
        analysisText = responseData.toString();
      }

      // Add context about the prompt used
      final fullDescription = 'AI Analysis: $analysisText';

      return VisionAnalysisResult(
        description: fullDescription,
        confidence: _extractConfidence(analysisText),
        detectedObjects: _extractObjects(analysisText),
        colors: _extractColors(analysisText),
        tags: _extractTags(analysisText),
        rawResponse: {'inference_api': responseData, 'prompt': prompt},
        isMockData: false,
      );
    } catch (e) {
      throw ApiException('Failed to parse Inference API response: $e');
    }
  }

  /// Parses the Spaces API response into a structured result
  VisionAnalysisResult _parseResponse(Map<String, dynamic> responseData) {
    try {
      // The response structure may vary, but typically contains a 'data' field
      final data = responseData['data'];

      if (data is List && data.isNotEmpty) {
        final analysisText = data[0] as String? ?? 'No analysis available';

        return VisionAnalysisResult(
          description: analysisText,
          confidence: _extractConfidence(analysisText),
          detectedObjects: _extractObjects(analysisText),
          colors: _extractColors(analysisText),
          tags: _extractTags(analysisText),
          rawResponse: responseData,
          isMockData: false,
        );
      } else {
        throw ApiException('Unexpected response format');
      }
    } catch (e) {
      throw ApiException('Failed to parse API response: $e');
    }
  }
  
  /// Extracts confidence score from analysis text (basic implementation)
  double _extractConfidence(String text) {
    // This is a simple heuristic - in a real implementation,
    // you might use more sophisticated text analysis
    if (text.toLowerCase().contains('clearly') || 
        text.toLowerCase().contains('definitely')) {
      return 0.9;
    } else if (text.toLowerCase().contains('appears') || 
               text.toLowerCase().contains('seems')) {
      return 0.7;
    } else if (text.toLowerCase().contains('possibly') || 
               text.toLowerCase().contains('might')) {
      return 0.5;
    }
    return 0.8; // Default confidence
  }
  
  /// Extracts detected objects from analysis text
  List<String> _extractObjects(String text) {
    final objects = <String>[];
    final commonObjects = [
      'person', 'people', 'man', 'woman', 'child', 'baby',
      'car', 'truck', 'bus', 'motorcycle', 'bicycle',
      'dog', 'cat', 'bird', 'horse', 'cow', 'sheep',
      'tree', 'flower', 'grass', 'mountain', 'sky', 'cloud',
      'building', 'house', 'road', 'bridge', 'water', 'ocean',
      'food', 'plate', 'cup', 'bottle', 'chair', 'table',
    ];
    
    for (final object in commonObjects) {
      if (text.toLowerCase().contains(object)) {
        objects.add(object);
      }
    }
    
    return objects.take(5).toList(); // Limit to top 5
  }
  
  /// Extracts color information from analysis text
  List<String> _extractColors(String text) {
    final colors = <String>[];
    final colorWords = [
      'red', 'blue', 'green', 'yellow', 'orange', 'purple',
      'pink', 'brown', 'black', 'white', 'gray', 'grey',
    ];
    
    for (final color in colorWords) {
      if (text.toLowerCase().contains(color)) {
        colors.add(color);
      }
    }
    
    return colors.take(3).toList(); // Limit to top 3
  }
  
  /// Extracts relevant tags from analysis text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final tagWords = [
      'outdoor', 'indoor', 'nature', 'urban', 'portrait',
      'landscape', 'close-up', 'wide shot', 'bright', 'dark',
      'colorful', 'monochrome', 'vintage', 'modern',
    ];
    
    for (final tag in tagWords) {
      if (text.toLowerCase().contains(tag)) {
        tags.add(tag);
      }
    }
    
    return tags.take(4).toList(); // Limit to top 4
  }

  /// Parses the response for item detection, attempting multiple parsing strategies
  ItemDetectionResult _parseItemDetectionResponse(String responseText, Map<String, dynamic> rawResponse) {
    try {
      print('🔍 Parsing item detection response...');
      print('📝 Full response text: $responseText');

      // Remove common prefixes
      String cleanedText = responseText;
      final prefixes = ['AI Vision Analysis: ', 'AI Analysis: ', 'Analysis: '];
      for (final prefix in prefixes) {
        if (cleanedText.startsWith(prefix)) {
          cleanedText = cleanedText.substring(prefix.length);
          break;
        }
      }

      // Strategy 1: Try to parse as JSON with multiple approaches
      try {
        // First, try to find complete JSON objects with "items" key
        final jsonMatches = RegExp(r'\{[^{}]*"items"[^{}]*\[[^\]]*\][^{}]*\}', dotAll: true).allMatches(cleanedText);

        for (final match in jsonMatches) {
          try {
            final jsonString = match.group(0)!;
            print('📊 Found potential JSON: $jsonString');
            final result = ItemDetectionResult.fromJson(jsonString);
            if (result.items.isNotEmpty) {
              print('✅ Successfully parsed ${result.items.length} items from JSON');
              return result;
            }
          } catch (e) {
            print('⚠️ JSON candidate failed: $e');
            continue;
          }
        }

        // Try to extract JSON from code blocks (```json ... ```)
        final codeBlockMatch = RegExp(r'```(?:json)?\s*(\{.*?\})\s*```', dotAll: true).firstMatch(cleanedText);
        if (codeBlockMatch != null) {
          try {
            final jsonString = codeBlockMatch.group(1)!;
            print('📊 Found JSON in code block: $jsonString');
            final result = ItemDetectionResult.fromJson(jsonString);
            if (result.items.isNotEmpty) {
              print('✅ Successfully parsed ${result.items.length} items from code block JSON');
              return result;
            }
          } catch (e) {
            print('⚠️ Code block JSON parsing failed: $e');
          }
        }

        // Try broader JSON extraction (any JSON object)
        final allJsonMatches = RegExp(r'\{[^{}]*\}', dotAll: true).allMatches(cleanedText);
        for (final match in allJsonMatches) {
          try {
            final jsonString = match.group(0)!;
            if (jsonString.contains('items') || jsonString.contains('name')) {
              print('📊 Trying JSON match: ${jsonString.substring(0, jsonString.length > 100 ? 100 : jsonString.length)}...');
              final result = ItemDetectionResult.fromJson(jsonString);
              if (result.items.isNotEmpty) {
                print('✅ Successfully parsed ${result.items.length} items from broad JSON');
                return result;
              }
            }
          } catch (e) {
            print('⚠️ Broad JSON parsing failed: $e');
            continue;
          }
        }

        // Try to parse the entire cleaned text as JSON
        if (cleanedText.trim().startsWith('{') && cleanedText.trim().endsWith('}')) {
          try {
            print('📊 Trying to parse entire response as JSON');
            final result = ItemDetectionResult.fromJson(cleanedText.trim());
            if (result.items.isNotEmpty) {
              print('✅ Successfully parsed ${result.items.length} items from full response JSON');
              return result;
            }
          } catch (e) {
            print('⚠️ Full response JSON parsing failed: $e');
          }
        }
      } catch (e) {
        print('⚠️ JSON parsing strategy failed: $e');
      }

      // Strategy 2: Enhanced text parsing with product detection
      print('📝 Using enhanced text parsing...');
      final result = _parseTextForItems(cleanedText);
      print('✅ Enhanced text parsing found ${result.items.length} items');

      if (result.items.isNotEmpty) {
        return result;
      }

      // Strategy 3: Fallback to original text parsing
      print('📝 Using original text parsing fallback...');
      final fallbackResult = ItemDetectionResult.fromText(cleanedText);
      print('✅ Original text parsing found ${fallbackResult.items.length} items');
      return fallbackResult;

    } catch (e) {
      print('🚨 Error parsing item detection response: $e');
      return ItemDetectionResult(
        items: [],
        rawResponse: responseText,
        errorMessage: 'Failed to parse response: $e',
      );
    }
  }

  /// Enhanced text parsing specifically for grocery items
  ItemDetectionResult _parseTextForItems(String text) {
    final items = <DetectedItem>[];
    final lowerText = text.toLowerCase();

    print('🔍 Enhanced text parsing input: $text');

    // Expanded grocery brands and products to look for
    final productPatterns = {
      // Beer brands
      'budweiser': {'brand': 'Budweiser', 'type': 'Beer', 'productType': 'Can'},
      'corona': {'brand': 'Corona', 'type': 'Beer', 'productType': 'Bottle'},
      'heineken': {'brand': 'Heineken', 'type': 'Beer', 'productType': 'Can'},
      'stella artois': {'brand': 'Stella Artois', 'type': 'Beer', 'productType': 'Bottle'},
      'stella': {'brand': 'Stella Artois', 'type': 'Beer', 'productType': 'Bottle'},
      'desperados': {'brand': 'Desperados', 'type': 'Beer', 'productType': 'Bottle'},
      'carlsberg': {'brand': 'Carlsberg', 'type': 'Beer', 'productType': 'Can'},
      'guinness': {'brand': 'Guinness', 'type': 'Beer', 'productType': 'Can'},
      'miller': {'brand': 'Miller', 'type': 'Beer', 'productType': 'Can'},
      'coors': {'brand': 'Coors', 'type': 'Beer', 'productType': 'Can'},

      // Soft drinks
      'coca-cola': {'brand': 'Coca-Cola', 'type': 'Soft Drink', 'productType': 'Can'},
      'coca cola': {'brand': 'Coca-Cola', 'type': 'Soft Drink', 'productType': 'Can'},
      'coke': {'brand': 'Coca-Cola', 'type': 'Soft Drink', 'productType': 'Can'},
      'pepsi': {'brand': 'Pepsi', 'type': 'Soft Drink', 'productType': 'Can'},
      'sprite': {'brand': 'Sprite', 'type': 'Soft Drink', 'productType': 'Can'},
      'fanta': {'brand': 'Fanta', 'type': 'Soft Drink', 'productType': 'Can'},
      'dr pepper': {'brand': 'Dr Pepper', 'type': 'Soft Drink', 'productType': 'Can'},
      'mountain dew': {'brand': 'Mountain Dew', 'type': 'Soft Drink', 'productType': 'Can'},
      '7up': {'brand': '7UP', 'type': 'Soft Drink', 'productType': 'Can'},

      // Water brands
      'evian': {'brand': 'Evian', 'type': 'Water', 'productType': 'Bottle'},
      'perrier': {'brand': 'Perrier', 'type': 'Water', 'productType': 'Bottle'},
      'vittel': {'brand': 'Vittel', 'type': 'Water', 'productType': 'Bottle'},
      'dasani': {'brand': 'Dasani', 'type': 'Water', 'productType': 'Bottle'},
      'aquafina': {'brand': 'Aquafina', 'type': 'Water', 'productType': 'Bottle'},

      // Energy drinks
      'red bull': {'brand': 'Red Bull', 'type': 'Energy Drink', 'productType': 'Can'},
      'monster': {'brand': 'Monster', 'type': 'Energy Drink', 'productType': 'Can'},
      'rockstar': {'brand': 'Rockstar', 'type': 'Energy Drink', 'productType': 'Can'},

      // Snacks
      'lays': {'brand': 'Lay\'s', 'type': 'Chips', 'productType': 'Bag'},
      'pringles': {'brand': 'Pringles', 'type': 'Chips', 'productType': 'Can'},
      'doritos': {'brand': 'Doritos', 'type': 'Chips', 'productType': 'Bag'},
      'cheetos': {'brand': 'Cheetos', 'type': 'Snacks', 'productType': 'Bag'},
    };

    // Look for specific product mentions
    for (final entry in productPatterns.entries) {
      final productName = entry.key;
      final productInfo = entry.value;

      if (lowerText.contains(productName)) {
        // Try to extract price if mentioned nearby
        String? price = _extractPriceNear(text, productName);

        // Try to extract size if mentioned
        String? size = _extractSizeNear(text, productName);

        items.add(DetectedItem(
          name: productInfo['brand']! + (productInfo['type'] != null ? ' ${productInfo['type']}' : ''),
          brand: productInfo['brand'],
          productType: productInfo['productType'],
          size: size,
          price: price,
          description: 'Detected from text analysis',
          confidence: 0.7,
        ));
      }
    }

    // Look for generic product indicators with more comprehensive patterns
    final genericPatterns = [
      // Beverages
      {'pattern': r'beer.*can', 'type': 'Beer', 'productType': 'Can'},
      {'pattern': r'beer.*bottle', 'type': 'Beer', 'productType': 'Bottle'},
      {'pattern': r'can.*beer', 'type': 'Beer', 'productType': 'Can'},
      {'pattern': r'bottle.*beer', 'type': 'Beer', 'productType': 'Bottle'},
      {'pattern': r'soda.*can', 'type': 'Soft Drink', 'productType': 'Can'},
      {'pattern': r'can.*soda', 'type': 'Soft Drink', 'productType': 'Can'},
      {'pattern': r'soft.*drink', 'type': 'Soft Drink', 'productType': 'Can'},
      {'pattern': r'water.*bottle', 'type': 'Water', 'productType': 'Bottle'},
      {'pattern': r'bottle.*water', 'type': 'Water', 'productType': 'Bottle'},
      {'pattern': r'energy.*drink', 'type': 'Energy Drink', 'productType': 'Can'},
      {'pattern': r'juice.*box', 'type': 'Juice', 'productType': 'Box'},
      {'pattern': r'juice.*bottle', 'type': 'Juice', 'productType': 'Bottle'},

      // Food items
      {'pattern': r'chips.*bag', 'type': 'Chips', 'productType': 'Bag'},
      {'pattern': r'bag.*chips', 'type': 'Chips', 'productType': 'Bag'},
      {'pattern': r'snack.*bag', 'type': 'Snacks', 'productType': 'Bag'},
      {'pattern': r'candy.*bar', 'type': 'Candy', 'productType': 'Bar'},
      {'pattern': r'chocolate.*bar', 'type': 'Chocolate', 'productType': 'Bar'},
      {'pattern': r'cereal.*box', 'type': 'Cereal', 'productType': 'Box'},
      {'pattern': r'crackers.*box', 'type': 'Crackers', 'productType': 'Box'},

      // General product indicators
      {'pattern': r'package', 'type': 'Product', 'productType': 'Package'},
      {'pattern': r'container', 'type': 'Product', 'productType': 'Container'},
      {'pattern': r'bottle', 'type': 'Beverage', 'productType': 'Bottle'},
      {'pattern': r'can', 'type': 'Product', 'productType': 'Can'},
      {'pattern': r'box', 'type': 'Product', 'productType': 'Box'},
      {'pattern': r'bag', 'type': 'Product', 'productType': 'Bag'},
    ];

    for (final pattern in genericPatterns) {
      final regex = RegExp(pattern['pattern']!, caseSensitive: false);
      final matches = regex.allMatches(text);

      for (final match in matches) {
        // Try to extract more context around the match
        final matchStart = match.start;
        final matchEnd = match.end;
        final contextStart = (matchStart - 20).clamp(0, text.length);
        final contextEnd = (matchEnd + 20).clamp(0, text.length);
        final context = text.substring(contextStart, contextEnd);

        // Try to find brand or product name in the context
        String? extractedName = _extractProductNameFromContext(context);

        items.add(DetectedItem(
          name: extractedName ?? '${pattern['type']} Product',
          productType: pattern['productType'],
          description: 'Detected from pattern: ${match.group(0)}',
          confidence: extractedName != null ? 0.7 : 0.5,
        ));
      }
    }

    // Look for price indicators to find products
    final priceRegex = RegExp(r'[\$€£]\d+[.,]\d{2}|\d+[.,]\d{2}[\$€£]');
    final priceMatches = priceRegex.allMatches(text);

    for (final priceMatch in priceMatches) {
      final price = priceMatch.group(0)!;
      final priceStart = priceMatch.start;
      final contextStart = (priceStart - 50).clamp(0, text.length);
      final contextEnd = (priceStart + 20).clamp(0, text.length);
      final context = text.substring(contextStart, contextEnd);

      // Try to find product name near the price
      String? productName = _extractProductNameFromContext(context);
      if (productName != null && productName.isNotEmpty) {
        items.add(DetectedItem(
          name: productName,
          price: price,
          description: 'Product found near price',
          confidence: 0.6,
        ));
      }
    }

    // Remove duplicates based on name and brand
    final uniqueItems = <DetectedItem>[];
    for (final item in items) {
      final isDuplicate = uniqueItems.any((existing) =>
        existing.name == item.name && existing.brand == item.brand);
      if (!isDuplicate) {
        uniqueItems.add(item);
      }
    }

    return ItemDetectionResult(
      items: uniqueItems,
      rawResponse: text,
      isMockData: false,
    );
  }

  /// Extract price mentioned near a product name
  String? _extractPriceNear(String text, String productName) {
    final productIndex = text.toLowerCase().indexOf(productName.toLowerCase());
    if (productIndex == -1) return null;

    // Look for price patterns within 100 characters of the product name
    final start = (productIndex - 50).clamp(0, text.length);
    final end = (productIndex + productName.length + 50).clamp(0, text.length);
    final nearbyText = text.substring(start, end);

    // Price patterns: $X.XX, €X.XX, £X.XX, X.XX€, etc.
    final priceRegex = RegExp(r'[\$€£]?\d+[.,]\d{2}[\$€£]?');
    final match = priceRegex.firstMatch(nearbyText);
    return match?.group(0);
  }

  /// Extract size mentioned near a product name
  String? _extractSizeNear(String text, String productName) {
    final productIndex = text.toLowerCase().indexOf(productName.toLowerCase());
    if (productIndex == -1) return null;

    // Look for size patterns within 50 characters of the product name
    final start = (productIndex - 25).clamp(0, text.length);
    final end = (productIndex + productName.length + 25).clamp(0, text.length);
    final nearbyText = text.substring(start, end);

    // Size patterns: 330ml, 500ML, 1L, 12-pack, etc.
    final sizeRegex = RegExp(r'\d+\s*(?:ml|ML|l|L|cl|CL|pack|Pack|oz|OZ)', caseSensitive: false);
    final match = sizeRegex.firstMatch(nearbyText);
    return match?.group(0);
  }

  /// Extract product name from context using various patterns
  String? _extractProductNameFromContext(String context) {
    final lowerContext = context.toLowerCase();

    // Common brand patterns
    final brandPatterns = [
      'coca-cola', 'coca cola', 'coke', 'pepsi', 'sprite', 'fanta',
      'budweiser', 'corona', 'heineken', 'stella', 'carlsberg',
      'evian', 'perrier', 'vittel', 'dasani', 'aquafina',
      'red bull', 'monster', 'rockstar',
      'lays', 'lay\'s', 'pringles', 'doritos', 'cheetos'
    ];

    for (final brand in brandPatterns) {
      if (lowerContext.contains(brand)) {
        // Try to extract the full product name around the brand
        final brandIndex = lowerContext.indexOf(brand);
        final start = (brandIndex - 10).clamp(0, context.length);
        final end = (brandIndex + brand.length + 20).clamp(0, context.length);
        final productContext = context.substring(start, end).trim();

        // Clean up the extracted name
        final cleanName = productContext
            .replaceAll(RegExp(r'[^\w\s\-]'), ' ')
            .replaceAll(RegExp(r'\s+'), ' ')
            .trim();

        if (cleanName.isNotEmpty && cleanName.length > brand.length) {
          return cleanName;
        }

        // Fallback to just the brand name
        return brand.split(' ').map((word) =>
          word[0].toUpperCase() + word.substring(1)).join(' ');
      }
    }

    // Look for capitalized words that might be product names
    final capitalizedWords = RegExp(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b');
    final matches = capitalizedWords.allMatches(context);

    for (final match in matches) {
      final word = match.group(0)!;
      if (word.length > 3 && !['The', 'And', 'For', 'With'].contains(word)) {
        return word;
      }
    }

    return null;
  }

  /// Generates a random session hash for Gradio API calls
  String _generateSessionHash() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random();
    return List.generate(12, (index) => chars[random.nextInt(chars.length)]).join();
  }
}

/// Result class for vision analysis
class VisionAnalysisResult {
  final String description;
  final double confidence;
  final List<String> detectedObjects;
  final List<String> colors;
  final List<String> tags;
  final Map<String, dynamic> rawResponse;
  final bool isMockData;

  const VisionAnalysisResult({
    required this.description,
    required this.confidence,
    required this.detectedObjects,
    required this.colors,
    required this.tags,
    required this.rawResponse,
    this.isMockData = false,
  });
  
  /// Creates a mock result for testing purposes
  factory VisionAnalysisResult.mock() {
    return VisionAnalysisResult(
      description: "⚠️ MOCK DATA: This is demonstration data only. The image shows a beautiful landscape with mountains in the background and a clear blue sky. There are trees in the foreground and the lighting suggests it was taken during golden hour. (Real AI analysis was not available - check console logs for details)",
      confidence: 0.85,
      detectedObjects: const ['mountain', 'tree', 'sky', 'landscape'],
      colors: const ['blue', 'green', 'brown'],
      tags: const ['outdoor', 'nature', 'landscape', 'bright'],
      rawResponse: {
        'mock': true,
        'reason': 'API unavailable',
        'timestamp': DateTime.now().toIso8601String()
      },
      isMockData: true,
    );
  }
}

/// Custom exception for API-related errors
class ApiException implements Exception {
  final String message;
  
  const ApiException(this.message);
  
  @override
  String toString() => 'ApiException: $message';
}
